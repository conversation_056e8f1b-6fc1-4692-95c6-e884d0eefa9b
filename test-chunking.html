<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能分块测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #e8f5e8;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9em;
        }
        button {
            background: #006400;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #004d00;
        }
        .config {
            margin: 10px 0;
        }
        select, input {
            padding: 5px;
            margin: 0 5px;
        }
    </style>
</head>
<body>
    <h1>🧠 智能分块测试</h1>
    
    <div class="test-section">
        <h3>分块配置</h3>
        <div class="config">
            <label>目标分块大小：</label>
            <select id="chunkSize">
                <option value="1">1MB</option>
                <option value="2">2MB</option>
                <option value="5" selected>5MB</option>
                <option value="10">10MB</option>
            </select>
        </div>
        <div class="config">
            <label>测试数据量：</label>
            <select id="dataSize">
                <option value="100">100个章节</option>
                <option value="500" selected>500个章节</option>
                <option value="1000">1000个章节</option>
                <option value="2000">2000个章节</option>
            </select>
        </div>
        <button onclick="runChunkingTest()">🧪 运行分块测试</button>
        <button onclick="compareChunkingMethods()">📊 对比分块方法</button>
    </div>

    <div class="test-section">
        <h3>测试结果</h3>
        <div id="testResults"></div>
    </div>

    <script>
        // 模拟索引数据生成
        function generateMockIndexData(count) {
            const data = [];
            for (let i = 0; i < count; i++) {
                data.push({
                    t: `第${i + 1}篇 测试章节标题 - 这是一个较长的标题用于测试`,
                    c: `这是第${i + 1}个章节的内容。`.repeat(Math.floor(Math.random() * 100) + 50),
                    bn: `测试书籍${Math.floor(i / 50) + 1}`,
                    bp: `test-book-${Math.floor(i / 50) + 1}`,
                    cn: `第${(i % 50) + 1}章`,
                    cp: `test-book-${Math.floor(i / 50) + 1}/chapter-${(i % 50) + 1}`,
                    f: Math.random() > 0.7 ? {
                        [`${i}_1`]: { content: `脚注内容${i}_1`, sectionNumber: 1 },
                        [`${i}_2`]: { content: `脚注内容${i}_2`, sectionNumber: 2 }
                    } : undefined
                });
            }
            return data;
        }

        // 估算单个索引项的序列化大小
        function estimateItemSize(item) {
            let size = 0;
            
            size += (item.t || '').length * 2;
            size += (item.c || '').length * 2;
            size += (item.bn || '').length * 2;
            size += (item.bp || '').length * 2;
            size += (item.cn || '').length * 2;
            size += (item.cp || '').length * 2;
            
            if (item.f && typeof item.f === 'object') {
                const footnoteStr = JSON.stringify(item.f);
                size += footnoteStr.length * 2;
            }
            
            size += 200; // JSON结构开销
            return size;
        }

        // 智能分块（按文件大小）
        function createSmartChunks(items, targetChunkSize) {
            const chunks = [];
            let currentChunk = [];
            let currentChunkSize = 0;
            const jsonOverhead = 50;
            
            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                const itemSize = estimateItemSize(item) + jsonOverhead;
                
                if (currentChunk.length > 0 && (currentChunkSize + itemSize) > targetChunkSize) {
                    chunks.push(currentChunk);
                    currentChunk = [item];
                    currentChunkSize = itemSize;
                } else {
                    currentChunk.push(item);
                    currentChunkSize += itemSize;
                }
            }
            
            if (currentChunk.length > 0) {
                chunks.push(currentChunk);
            }
            
            return chunks;
        }

        // 传统分块（按章节数）
        function createLegacyChunks(items, chunkSize = 50) {
            const chunks = [];
            for (let i = 0; i < items.length; i += chunkSize) {
                chunks.push(items.slice(i, i + chunkSize));
            }
            return chunks;
        }

        // 运行分块测试
        function runChunkingTest() {
            const chunkSizeMB = parseInt(document.getElementById('chunkSize').value);
            const dataCount = parseInt(document.getElementById('dataSize').value);
            
            console.log(`开始测试：${dataCount}个章节，目标分块大小${chunkSizeMB}MB`);
            
            // 生成测试数据
            const testData = generateMockIndexData(dataCount);
            const totalDataSize = JSON.stringify(testData).length;
            
            // 智能分块
            const targetChunkSize = chunkSizeMB * 1024 * 1024;
            const smartChunks = createSmartChunks(testData, targetChunkSize);
            
            // 计算实际分块大小
            const chunkSizes = smartChunks.map(chunk => {
                const size = JSON.stringify(chunk).length;
                return {
                    itemCount: chunk.length,
                    sizeBytes: size,
                    sizeMB: (size / 1024 / 1024).toFixed(2)
                };
            });
            
            // 显示结果
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = `
                <div class="result">
                    <strong>📊 智能分块测试结果</strong><br>
                    总数据大小: ${(totalDataSize / 1024 / 1024).toFixed(2)}MB<br>
                    目标分块大小: ${chunkSizeMB}MB<br>
                    实际分块数量: ${smartChunks.length}个<br>
                    <br>
                    <strong>各分块详情:</strong><br>
                    ${chunkSizes.map((chunk, index) => 
                        `分块${index + 1}: ${chunk.itemCount}项, ${chunk.sizeMB}MB`
                    ).join('<br>')}
                    <br><br>
                    <strong>统计信息:</strong><br>
                    平均分块大小: ${(chunkSizes.reduce((sum, chunk) => sum + parseFloat(chunk.sizeMB), 0) / chunkSizes.length).toFixed(2)}MB<br>
                    最大分块: ${Math.max(...chunkSizes.map(c => parseFloat(c.sizeMB))).toFixed(2)}MB<br>
                    最小分块: ${Math.min(...chunkSizes.map(c => parseFloat(c.sizeMB))).toFixed(2)}MB
                </div>
            `;
        }

        // 对比分块方法
        function compareChunkingMethods() {
            const dataCount = parseInt(document.getElementById('dataSize').value);
            const testData = generateMockIndexData(dataCount);
            const totalDataSize = JSON.stringify(testData).length;
            
            // 智能分块
            const smartChunks = createSmartChunks(testData, 5 * 1024 * 1024); // 5MB
            const smartSizes = smartChunks.map(chunk => JSON.stringify(chunk).length);
            
            // 传统分块
            const legacyChunks = createLegacyChunks(testData, 50); // 50个章节
            const legacySizes = legacyChunks.map(chunk => JSON.stringify(chunk).length);
            
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = `
                <div class="result">
                    <strong>📈 分块方法对比</strong><br>
                    测试数据: ${dataCount}个章节, ${(totalDataSize / 1024 / 1024).toFixed(2)}MB<br>
                    <br>
                    <strong>🧠 智能分块 (按大小):</strong><br>
                    分块数量: ${smartChunks.length}个<br>
                    平均大小: ${(smartSizes.reduce((a, b) => a + b, 0) / smartSizes.length / 1024 / 1024).toFixed(2)}MB<br>
                    大小范围: ${(Math.min(...smartSizes) / 1024 / 1024).toFixed(2)}MB - ${(Math.max(...smartSizes) / 1024 / 1024).toFixed(2)}MB<br>
                    大小标准差: ${calculateStandardDeviation(smartSizes.map(s => s / 1024 / 1024)).toFixed(2)}MB<br>
                    <br>
                    <strong>📦 传统分块 (按数量):</strong><br>
                    分块数量: ${legacyChunks.length}个<br>
                    平均大小: ${(legacySizes.reduce((a, b) => a + b, 0) / legacySizes.length / 1024 / 1024).toFixed(2)}MB<br>
                    大小范围: ${(Math.min(...legacySizes) / 1024 / 1024).toFixed(2)}MB - ${(Math.max(...legacySizes) / 1024 / 1024).toFixed(2)}MB<br>
                    大小标准差: ${calculateStandardDeviation(legacySizes.map(s => s / 1024 / 1024)).toFixed(2)}MB<br>
                    <br>
                    <strong>🎯 结论:</strong><br>
                    智能分块的大小更加均匀 (标准差更小)，有利于网络传输优化
                </div>
            `;
        }

        // 计算标准差
        function calculateStandardDeviation(values) {
            const mean = values.reduce((a, b) => a + b, 0) / values.length;
            const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
            const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / squaredDiffs.length;
            return Math.sqrt(avgSquaredDiff);
        }
    </script>
</body>
</html>
